# CAPTCHA字符坐标检测器

一个强大的Python工具，用于解析HTML页面中的点击式CAPTCHA验证码，自动提取指定字符序列并精确定位其在图像中的坐标位置。

## 功能特性

- 🔍 **智能解析**: 自动解析HTML页面中的CAPTCHA指令和图像
- 🎯 **精确定位**: 使用ddddocr进行字符检测和坐标定位
- 📝 **指令提取**: 支持多种指令格式的字符序列提取
- 🖼️ **图像处理**: 支持base64编码的图像数据处理
- 🔧 **可扩展**: 模块化设计，易于扩展和定制
- 📊 **调试支持**: 提供可视化调试图像和详细日志
- 🤖 **自动化**: 支持自动化点击操作模拟

## 安装依赖

```bash
pip install -r requirements.txt
```

主要依赖包：
- `ddddocr`: 核心OCR识别库
- `opencv-python`: 图像处理
- `Pillow`: 图像操作
- `beautifulsoup4`: HTML解析
- `numpy`: 数值计算

## 快速开始

### 基本使用

```python
from captcha_solver import CaptchaCoordinateDetector

# 创建检测器实例
detector = CaptchaCoordinateDetector()

# 解决CAPTCHA
result = detector.solve_captcha('1.html')

# 获取点击坐标
print("点击坐标序列:")
for i, (char, coord) in enumerate(zip(result['target_characters'], result['click_coordinates'])):
    print(f"{i+1}. 字符 '{char}': ({coord[0]}, {coord[1]})")
```

### 运行示例

```bash
python example_usage.py
```

## 支持的CAPTCHA格式

### 指令格式
- `请依次点击【日,无,逊,莽】`
- `请按顺序点击【春,夏,秋,冬】`
- `依次点击【上,下,左,右】`
- `点击【东,南,西,北】`

### 图像格式
- PNG, JPG, JPEG, BMP, GIF, WebP
- Base64编码的图像数据
- 支持各种尺寸的验证码图像

## API文档

### CaptchaCoordinateDetector

主要的CAPTCHA检测器类。

#### 方法

##### `solve_captcha(html_file_path: str) -> Dict`
解决CAPTCHA验证码的主要方法。

**参数:**
- `html_file_path`: HTML文件路径

**返回:**
```python
{
    'instruction': '请依次点击【日,无,逊,莽】',
    'target_characters': ['日', '无', '逊', '莽'],
    'click_coordinates': [(100, 50), (200, 75), (300, 60), (150, 120)],
    'detected_characters': [...],  # 所有检测到的字符信息
    'image_size': (400, 200)
}
```

##### `parse_html_captcha(html_content: str) -> Dict`
解析HTML内容中的CAPTCHA信息。

##### `extract_target_characters(instruction: str) -> List[str]`
从指令文本中提取目标字符序列。

##### `detect_characters_in_image(image_data: bytes) -> List[Dict]`
检测图像中的所有字符及其位置。

##### `find_character_coordinates(target_chars: List[str], detected_chars: List[Dict]) -> List[Tuple[int, int]]`
根据目标字符找到对应的坐标。

### CaptchaAutomator

自动化操作器类。

#### 方法

##### `simulate_clicks(coordinates: List[Tuple[int, int]], delay: float = 0.5)`
模拟点击操作。

## 配置选项

在 `config.py` 中可以调整各种配置参数：

```python
# OCR配置
OCR_CONFIG = {
    'show_ad': False,
    'det_model_type': 'default',
    'ocr_model_type': 'default',
}

# 检测配置
DETECTION_CONFIG = {
    'confidence_threshold': 0.5,
    'nms_threshold': 0.4,
    'max_detections': 50,
}

# 调试配置
DEBUG_CONFIG = {
    'save_debug_image': True,
    'debug_image_path': 'debug_captcha.png',
    'log_level': 'INFO',
}
```

## 使用示例

### 1. 基本字符检测

```python
detector = CaptchaCoordinateDetector()
result = detector.solve_captcha('captcha.html')

print(f"需要点击的字符: {result['target_characters']}")
print(f"对应坐标: {result['click_coordinates']}")
```

### 2. 批量处理

```python
html_files = ['captcha1.html', 'captcha2.html', 'captcha3.html']
results = []

for html_file in html_files:
    try:
        result = detector.solve_captcha(html_file)
        results.append(result)
        print(f"✓ 成功处理 {html_file}")
    except Exception as e:
        print(f"✗ 处理失败 {html_file}: {e}")
```

### 3. 自动化点击

```python
from captcha_solver import CaptchaAutomator

detector = CaptchaCoordinateDetector()
automator = CaptchaAutomator(detector)

result = detector.solve_captcha('captcha.html')
automator.simulate_clicks(result['click_coordinates'], delay=0.5)
```

## 调试功能

### 可视化调试

程序会自动生成调试图像，显示：
- 检测到的字符边界框
- 字符识别结果
- 目标点击坐标标记

### 日志输出

详细的日志信息帮助调试：
```
2024-01-01 12:00:00 - INFO - CAPTCHA检测器初始化成功
2024-01-01 12:00:01 - INFO - 找到验证码指令: 请依次点击【日,无,逊,莽】
2024-01-01 12:00:02 - INFO - 提取到目标字符: ['日', '无', '逊', '莽']
2024-01-01 12:00:03 - INFO - 检测到 8 个字符区域
2024-01-01 12:00:04 - INFO - 识别字符: '日' 位置: (100, 50)
```

## 注意事项

1. **模型下载**: 首次使用ddddocr时会自动下载模型文件
2. **图像质量**: 确保CAPTCHA图像清晰，字符可识别
3. **字符编码**: 支持中文字符的识别和处理
4. **坐标系统**: 坐标原点在图像左上角，单位为像素

## 故障排除

### 常见问题

1. **ddddocr安装失败**
   ```bash
   pip install ddddocr -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

2. **字符识别不准确**
   - 检查图像质量
   - 调整检测阈值
   - 使用调试图像分析问题

3. **坐标偏移**
   - 确认图像尺寸设置正确
   - 检查HTML中的图像属性

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的CAPTCHA字符检测
- 提供完整的API和示例代码
