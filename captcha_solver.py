#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAPTCHA字符坐标检测器
支持解析HTML页面中的点击式验证码，提取指定字符序列并定位其在图像中的坐标
"""

import re
import base64
import io
import cv2
import numpy as np
from PIL import Image
from bs4 import BeautifulSoup
import ddddocr
from typing import List, Tuple, Dict, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CaptchaCoordinateDetector:
    """CAPTCHA字符坐标检测器"""
    
    def __init__(self):
        """初始化检测器"""
        try:
            # 初始化ddddocr检测器
            self.det = ddddocr.DdddOcr(det=True)  # 目标检测模式
            self.ocr = ddddocr.DdddOcr(show_ad=False)  # OCR识别模式
            logger.info("CAPTCHA检测器初始化成功")
        except Exception as e:
            logger.error(f"初始化ddddocr失败: {e}")
            raise
    
    def parse_html_captcha(self, html_content: str) -> Dict:
        """
        解析HTML页面中的CAPTCHA信息
        
        Args:
            html_content: HTML页面内容
            
        Returns:
            包含指令文本和图像数据的字典
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找验证码指令文本
            instruction_element = soup.find('span', class_='verify-msg')
            if not instruction_element:
                raise ValueError("未找到验证码指令元素")
            
            instruction_text = instruction_element.get_text().strip()
            logger.info(f"找到验证码指令: {instruction_text}")
            
            # 查找验证码图像
            img_element = soup.find('img', class_='back-img')
            if not img_element:
                raise ValueError("未找到验证码图像元素")
            
            # 提取base64图像数据
            src = img_element.get('src', '')
            if not src.startswith('data:image/'):
                raise ValueError("图像源格式不正确")
            
            # 解析base64数据
            base64_data = src.split(',')[1]
            image_data = base64.b64decode(base64_data)
            
            return {
                'instruction': instruction_text,
                'image_data': image_data,
                'image_width': int(img_element.get('width', '400').replace('px', '')),
                'image_height': int(img_element.get('height', '200').replace('px', ''))
            }
            
        except Exception as e:
            logger.error(f"解析HTML失败: {e}")
            raise
    
    def extract_target_characters(self, instruction: str) -> List[str]:
        """
        从指令文本中提取需要点击的字符序列
        
        Args:
            instruction: 指令文本，如"请依次点击【日,无,逊,莽】"
            
        Returns:
            字符列表
        """
        try:
            # 使用正则表达式提取方括号内的字符
            pattern = r'【([^】]+)】'
            match = re.search(pattern, instruction)
            
            if not match:
                raise ValueError(f"无法从指令中提取字符序列: {instruction}")
            
            characters_str = match.group(1)
            # 分割字符，去除空格
            characters = [char.strip() for char in characters_str.split(',') if char.strip()]
            
            logger.info(f"提取到目标字符: {characters}")
            return characters
            
        except Exception as e:
            logger.error(f"提取字符序列失败: {e}")
            raise
    
    def detect_characters_in_image(self, image_data: bytes) -> List[Dict]:
        """
        在图像中检测所有字符及其位置
        
        Args:
            image_data: 图像二进制数据
            
        Returns:
            字符检测结果列表，每个元素包含字符和坐标信息
        """
        try:
            # 使用ddddocr进行目标检测
            bboxes = self.det.detection(image_data)
            logger.info(f"检测到 {len(bboxes)} 个字符区域")
            
            # 加载图像用于字符识别
            image = Image.open(io.BytesIO(image_data))
            
            detected_chars = []
            
            for bbox in bboxes:
                x1, y1, x2, y2 = bbox
                
                # 裁剪字符区域
                char_image = image.crop((x1, y1, x2, y2))
                
                # 转换为字节数据进行OCR识别
                char_bytes = io.BytesIO()
                char_image.save(char_bytes, format='PNG')
                char_data = char_bytes.getvalue()
                
                # 识别字符
                try:
                    char_text = self.ocr.classification(char_data).strip()
                    
                    # 计算字符中心坐标
                    center_x = (x1 + x2) // 2
                    center_y = (y1 + y2) // 2
                    
                    detected_chars.append({
                        'character': char_text,
                        'bbox': (x1, y1, x2, y2),
                        'center': (center_x, center_y),
                        'confidence': 1.0  # ddddocr不提供置信度，设为1.0
                    })
                    
                    logger.info(f"识别字符: '{char_text}' 位置: ({center_x}, {center_y})")
                    
                except Exception as e:
                    logger.warning(f"识别字符失败: {e}")
                    continue
            
            return detected_chars
            
        except Exception as e:
            logger.error(f"字符检测失败: {e}")
            raise
    
    def find_character_coordinates(self, target_chars: List[str], detected_chars: List[Dict]) -> List[Tuple[int, int]]:
        """
        根据目标字符序列找到对应的坐标

        Args:
            target_chars: 目标字符列表
            detected_chars: 检测到的字符列表

        Returns:
            按顺序排列的坐标列表
        """
        coordinates = []
        used_indices = set()  # 记录已使用的检测结果索引

        for target_char in target_chars:
            found = False
            best_match = None
            best_score = 0
            best_index = -1

            for i, detected in enumerate(detected_chars):
                if i in used_indices:
                    continue

                detected_char = detected['character']

                # 精确匹配
                if detected_char == target_char:
                    best_match = detected
                    best_score = 1.0
                    best_index = i
                    break

                # 模糊匹配：检查是否包含目标字符
                elif target_char in detected_char:
                    score = len(target_char) / len(detected_char)
                    if score > best_score:
                        best_match = detected
                        best_score = score
                        best_index = i

                # 反向匹配：检查检测到的字符是否包含在目标字符中
                elif detected_char in target_char:
                    score = len(detected_char) / len(target_char)
                    if score > best_score:
                        best_match = detected
                        best_score = score
                        best_index = i

            if best_match and best_score > 0.5:  # 设置最低匹配阈值
                coordinates.append(best_match['center'])
                used_indices.add(best_index)
                found = True
                logger.info(f"找到字符 '{target_char}' 坐标: {best_match['center']} (匹配: '{best_match['character']}', 得分: {best_score:.2f})")

            if not found:
                logger.warning(f"未找到字符 '{target_char}'")
                # 尝试更宽松的匹配
                for i, detected in enumerate(detected_chars):
                    if i in used_indices:
                        continue

                    # 检查字符相似性（简单的字符包含检查）
                    if self._is_similar_character(target_char, detected['character']):
                        coordinates.append(detected['center'])
                        used_indices.add(i)
                        logger.info(f"模糊匹配字符 '{target_char}' -> '{detected['character']}' 坐标: {detected['center']}")
                        break

        return coordinates

    def _is_similar_character(self, target: str, detected: str) -> bool:
        """
        检查两个字符是否相似

        Args:
            target: 目标字符
            detected: 检测到的字符

        Returns:
            是否相似
        """
        # 简单的相似性检查
        if len(target) == 1 and len(detected) == 1:
            return False  # 单字符必须精确匹配

        # 检查是否有共同的字符
        target_chars = set(target)
        detected_chars = set(detected)
        common_chars = target_chars.intersection(detected_chars)

        if common_chars:
            similarity = len(common_chars) / max(len(target_chars), len(detected_chars))
            return similarity > 0.5

        return False
    
    def solve_captcha(self, html_file_path: str) -> Dict:
        """
        解决CAPTCHA验证码
        
        Args:
            html_file_path: HTML文件路径
            
        Returns:
            包含解决方案的字典
        """
        try:
            # 读取HTML文件
            with open(html_file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 解析CAPTCHA信息
            captcha_info = self.parse_html_captcha(html_content)
            
            # 提取目标字符
            target_characters = self.extract_target_characters(captcha_info['instruction'])
            
            # 检测图像中的字符
            detected_characters = self.detect_characters_in_image(captcha_info['image_data'])
            
            # 找到目标字符的坐标
            click_coordinates = self.find_character_coordinates(target_characters, detected_characters)
            
            result = {
                'instruction': captcha_info['instruction'],
                'target_characters': target_characters,
                'click_coordinates': click_coordinates,
                'detected_characters': detected_characters,
                'image_size': (captcha_info['image_width'], captcha_info['image_height'])
            }
            
            logger.info("CAPTCHA解析完成")
            return result
            
        except Exception as e:
            logger.error(f"解决CAPTCHA失败: {e}")
            raise


    def save_debug_image(self, image_data: bytes, detected_chars: List[Dict],
                        target_coords: List[Tuple[int, int]], output_path: str = "debug_captcha.png"):
        """
        保存调试图像，标注检测到的字符和目标坐标

        Args:
            image_data: 原始图像数据
            detected_chars: 检测到的字符列表
            target_coords: 目标坐标列表
            output_path: 输出文件路径
        """
        try:
            # 加载图像
            image = Image.open(io.BytesIO(image_data))
            image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

            # 标注检测到的字符
            for char_info in detected_chars:
                x1, y1, x2, y2 = char_info['bbox']
                center_x, center_y = char_info['center']
                char = char_info['character']

                # 绘制边界框
                cv2.rectangle(image_cv, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # 绘制字符文本
                cv2.putText(image_cv, char, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX,
                           0.8, (0, 255, 0), 2)

                # 绘制中心点
                cv2.circle(image_cv, (center_x, center_y), 3, (0, 255, 0), -1)

            # 标注目标点击坐标
            for i, (x, y) in enumerate(target_coords):
                cv2.circle(image_cv, (x, y), 8, (0, 0, 255), 3)
                cv2.putText(image_cv, str(i+1), (x+10, y-10), cv2.FONT_HERSHEY_SIMPLEX,
                           0.8, (0, 0, 255), 2)

            # 保存图像
            cv2.imwrite(output_path, image_cv)
            logger.info(f"调试图像已保存到: {output_path}")

        except Exception as e:
            logger.error(f"保存调试图像失败: {e}")

    def validate_solution(self, target_chars: List[str], coordinates: List[Tuple[int, int]]) -> bool:
        """
        验证解决方案的有效性

        Args:
            target_chars: 目标字符列表
            coordinates: 坐标列表

        Returns:
            是否有效
        """
        if len(target_chars) != len(coordinates):
            logger.warning(f"字符数量({len(target_chars)})与坐标数量({len(coordinates)})不匹配")
            return False

        # 检查坐标是否合理
        for i, (x, y) in enumerate(coordinates):
            if x < 0 or y < 0:
                logger.warning(f"坐标 {i+1} 包含负值: ({x}, {y})")
                return False

        return True


class CaptchaAutomator:
    """CAPTCHA自动化操作器"""

    def __init__(self, detector: CaptchaCoordinateDetector):
        self.detector = detector

    def simulate_clicks(self, coordinates: List[Tuple[int, int]], delay: float = 0.5):
        """
        模拟点击操作（示例实现）

        Args:
            coordinates: 点击坐标列表
            delay: 点击间隔时间（秒）
        """
        try:
            import time

            logger.info("开始模拟点击操作...")

            for i, (x, y) in enumerate(coordinates):
                logger.info(f"点击 {i+1}: ({x}, {y})")

                # 这里可以集成实际的自动化工具，如selenium、pyautogui等
                # 示例：
                # pyautogui.click(x, y)

                time.sleep(delay)

            logger.info("点击操作完成")

        except Exception as e:
            logger.error(f"模拟点击失败: {e}")


def main():
    """主函数示例"""
    try:
        # 创建检测器实例
        detector = CaptchaCoordinateDetector()

        # 解决CAPTCHA
        result = detector.solve_captcha('1.html')

        # 验证解决方案
        is_valid = detector.validate_solution(
            result['target_characters'],
            result['click_coordinates']
        )

        print("=" * 50)
        print("CAPTCHA解决方案")
        print("=" * 50)
        print(f"指令: {result['instruction']}")
        print(f"目标字符: {result['target_characters']}")
        print(f"图像尺寸: {result['image_size']}")
        print(f"解决方案有效性: {'✓ 有效' if is_valid else '✗ 无效'}")
        print("\n点击坐标序列:")

        for i, (char, coord) in enumerate(zip(result['target_characters'], result['click_coordinates'])):
            print(f"  {i+1}. 字符 '{char}': ({coord[0]}, {coord[1]})")

        print(f"\n检测到的所有字符 ({len(result['detected_characters'])} 个):")
        for char_info in result['detected_characters']:
            print(f"  '{char_info['character']}': {char_info['center']}")

        # 保存调试图像
        if 'image_data' in locals():
            detector.save_debug_image(
                result.get('image_data', b''),
                result['detected_characters'],
                result['click_coordinates']
            )

        # 创建自动化操作器（可选）
        automator = CaptchaAutomator(detector)

        # 询问是否执行自动点击
        user_input = input("\n是否执行自动点击操作？(y/N): ").strip().lower()
        if user_input == 'y':
            automator.simulate_clicks(result['click_coordinates'])

    except Exception as e:
        logger.error(f"程序执行失败: {e}")


if __name__ == "__main__":
    main()
