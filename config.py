#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAPTCHA解决器配置文件
"""

# OCR配置
OCR_CONFIG = {
    'show_ad': False,  # 是否显示ddddocr广告
    'det_model_type': 'default',  # 检测模型类型
    'ocr_model_type': 'default',  # OCR模型类型
}

# 图像处理配置
IMAGE_CONFIG = {
    'min_char_width': 10,  # 最小字符宽度
    'min_char_height': 10,  # 最小字符高度
    'max_char_width': 100,  # 最大字符宽度
    'max_char_height': 100,  # 最大字符高度
}

# 坐标检测配置
DETECTION_CONFIG = {
    'confidence_threshold': 0.5,  # 置信度阈值
    'nms_threshold': 0.4,  # 非极大值抑制阈值
    'max_detections': 50,  # 最大检测数量
}

# 指令解析配置
INSTRUCTION_CONFIG = {
    'patterns': [
        r'请依次点击【([^】]+)】',  # 标准格式
        r'请按顺序点击【([^】]+)】',  # 变体格式1
        r'依次点击【([^】]+)】',     # 变体格式2
        r'点击【([^】]+)】',        # 简化格式
    ],
    'separators': [',', '，', ' ', '、'],  # 字符分隔符
}

# 调试配置
DEBUG_CONFIG = {
    'save_debug_image': True,  # 是否保存调试图像
    'debug_image_path': 'debug_captcha.png',  # 调试图像路径
    'log_level': 'INFO',  # 日志级别
}

# 自动化配置
AUTOMATION_CONFIG = {
    'click_delay': 0.5,  # 点击间隔时间（秒）
    'retry_count': 3,  # 重试次数
    'timeout': 30,  # 超时时间（秒）
}

# 支持的图像格式
SUPPORTED_FORMATS = ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp']

# 错误消息
ERROR_MESSAGES = {
    'html_parse_error': '解析HTML失败',
    'image_not_found': '未找到验证码图像',
    'instruction_not_found': '未找到验证码指令',
    'character_not_detected': '未检测到字符',
    'coordinate_invalid': '坐标无效',
    'ocr_init_error': 'OCR初始化失败',
}
