#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAPTCHA解决器使用示例
"""

import os
import sys
import json
from captcha_solver import CaptchaCoordinateDetector, CaptchaAutomator
from config import DEBUG_CONFIG, AUTOMATION_CONFIG
import logging

# 配置日志
logging.basicConfig(
    level=getattr(logging, DEBUG_CONFIG['log_level']),
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("基本使用示例")
    print("=" * 60)
    
    try:
        # 创建检测器
        detector = CaptchaCoordinateDetector()
        
        # 检查HTML文件是否存在
        html_file = '1.html'
        if not os.path.exists(html_file):
            print(f"错误: 找不到文件 {html_file}")
            return
        
        # 解决CAPTCHA
        result = detector.solve_captcha(html_file)
        
        # 显示结果
        print(f"指令: {result['instruction']}")
        print(f"目标字符: {result['target_characters']}")
        print(f"图像尺寸: {result['image_size']}")
        
        print("\n点击坐标:")
        for i, (char, coord) in enumerate(zip(result['target_characters'], result['click_coordinates'])):
            print(f"  {i+1}. '{char}' -> ({coord[0]}, {coord[1]})")
        
        return result
        
    except Exception as e:
        logger.error(f"基本使用示例失败: {e}")
        return None


def example_batch_processing():
    """批量处理示例"""
    print("\n" + "=" * 60)
    print("批量处理示例")
    print("=" * 60)
    
    try:
        detector = CaptchaCoordinateDetector()
        
        # 假设有多个HTML文件
        html_files = ['1.html']  # 可以添加更多文件
        results = []
        
        for html_file in html_files:
            if os.path.exists(html_file):
                print(f"\n处理文件: {html_file}")
                try:
                    result = detector.solve_captcha(html_file)
                    results.append({
                        'file': html_file,
                        'success': True,
                        'result': result
                    })
                    print(f"✓ 成功处理 {html_file}")
                except Exception as e:
                    results.append({
                        'file': html_file,
                        'success': False,
                        'error': str(e)
                    })
                    print(f"✗ 处理失败 {html_file}: {e}")
            else:
                print(f"✗ 文件不存在: {html_file}")
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        print(f"\n处理完成: {success_count}/{total_count} 成功")
        
        return results
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        return []


def example_custom_patterns():
    """自定义模式示例"""
    print("\n" + "=" * 60)
    print("自定义模式示例")
    print("=" * 60)
    
    try:
        detector = CaptchaCoordinateDetector()
        
        # 测试不同的指令格式
        test_instructions = [
            "请依次点击【日,无,逊,莽】",
            "请按顺序点击【春,夏,秋,冬】",
            "依次点击【上,下,左,右】",
            "点击【东,南,西,北】"
        ]
        
        for instruction in test_instructions:
            try:
                characters = detector.extract_target_characters(instruction)
                print(f"指令: {instruction}")
                print(f"提取字符: {characters}")
                print()
            except Exception as e:
                print(f"解析失败 '{instruction}': {e}")
                print()
        
    except Exception as e:
        logger.error(f"自定义模式示例失败: {e}")


def example_save_results():
    """保存结果示例"""
    print("\n" + "=" * 60)
    print("保存结果示例")
    print("=" * 60)
    
    try:
        detector = CaptchaCoordinateDetector()
        result = detector.solve_captcha('1.html')
        
        # 准备保存的数据
        save_data = {
            'instruction': result['instruction'],
            'target_characters': result['target_characters'],
            'click_coordinates': result['click_coordinates'],
            'image_size': result['image_size'],
            'detected_count': len(result['detected_characters'])
        }
        
        # 保存为JSON文件
        output_file = 'captcha_result.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        print(f"结果已保存到: {output_file}")
        
        # 显示保存的内容
        print("\n保存的内容:")
        print(json.dumps(save_data, ensure_ascii=False, indent=2))
        
    except Exception as e:
        logger.error(f"保存结果失败: {e}")


def example_automation_simulation():
    """自动化模拟示例"""
    print("\n" + "=" * 60)
    print("自动化模拟示例")
    print("=" * 60)
    
    try:
        detector = CaptchaCoordinateDetector()
        result = detector.solve_captcha('1.html')
        
        # 创建自动化操作器
        automator = CaptchaAutomator(detector)
        
        print("模拟自动点击操作...")
        print(f"将按顺序点击 {len(result['click_coordinates'])} 个坐标")
        
        # 显示点击计划
        for i, (char, coord) in enumerate(zip(result['target_characters'], result['click_coordinates'])):
            print(f"  步骤 {i+1}: 点击字符 '{char}' 位置 ({coord[0]}, {coord[1]})")
        
        # 执行模拟点击
        automator.simulate_clicks(
            result['click_coordinates'], 
            delay=AUTOMATION_CONFIG['click_delay']
        )
        
    except Exception as e:
        logger.error(f"自动化模拟失败: {e}")


def main():
    """主函数"""
    print("CAPTCHA字符坐标检测器 - 使用示例")
    print("支持的功能:")
    print("1. 解析HTML页面中的CAPTCHA指令")
    print("2. 提取需要点击的字符序列")
    print("3. 检测图像中的字符位置")
    print("4. 返回精确的点击坐标")
    print("5. 支持批量处理和自动化操作")
    
    # 检查依赖
    try:
        import ddddocr
        print("\n✓ ddddocr 已安装")
    except ImportError:
        print("\n✗ 请安装 ddddocr: pip install ddddocr")
        return
    
    try:
        import cv2
        print("✓ opencv-python 已安装")
    except ImportError:
        print("✗ 请安装 opencv-python: pip install opencv-python")
        return
    
    try:
        from bs4 import BeautifulSoup
        print("✓ beautifulsoup4 已安装")
    except ImportError:
        print("✗ 请安装 beautifulsoup4: pip install beautifulsoup4")
        return
    
    # 运行示例
    try:
        # 基本使用
        result = example_basic_usage()
        
        if result:
            # 其他示例
            example_batch_processing()
            example_custom_patterns()
            example_save_results()
            example_automation_simulation()
        
        print("\n" + "=" * 60)
        print("所有示例执行完成!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        logger.error(f"示例执行失败: {e}")


if __name__ == "__main__":
    main()
